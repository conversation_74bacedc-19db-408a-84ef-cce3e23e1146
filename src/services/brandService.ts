import { Brand, Post } from "@/types";
import { BRANDS } from "@/constants/brands";
import { MOCK_POSTS_DATA } from "@/constants/mockData";

// Create a mutable copy of brands for runtime modifications
let runtimeBrands: Brand[] = [...BRANDS];

export class BrandService {
  static getAllBrands(): Brand[] {
    return runtimeBrands;
  }

  static getBrandById(brandId: string): Brand | undefined {
    return runtimeBrands.find(brand => brand.id === brandId);
  }

  static addBrand(brandName: string): Promise<Brand> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Generate a unique ID from the brand name
        const brandId = brandName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

        // Ensure unique ID
        let uniqueId = brandId;
        let counter = 1;
        while (runtimeBrands.some(brand => brand.id === uniqueId)) {
          uniqueId = `${brandId}-${counter}`;
          counter++;
        }

        const newBrand: Brand = {
          id: uniqueId,
          name: brandName,
          pendingReplies: 0,
          status: "active"
        };

        runtimeBrands.push(newBrand);
        resolve(newBrand);
      }, 500);
    });
  }

  static getBrandPosts(brandId: string): Post[] {
    return MOCK_POSTS_DATA[brandId] || [];
  }

  static async fetchBrandPosts(brandId: string): Promise<Post[]> {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(this.getBrandPosts(brandId));
      }, 500);
    });
  }

  static sendReply(postId: string, commentId: string, reply: string): Promise<void> {
    // Simulate API call for sending reply
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log("Reply sent:", { postId, commentId, reply });
        resolve();
      }, 1000);
    });
  }

  static updateReply(postId: string, commentId: string, reply: string): Promise<void> {
    // Simulate API call for updating reply
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log("Reply updated:", { postId, commentId, reply });
        resolve();
      }, 1000);
    });
  }

  static deleteBrand(brandId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const brandIndex = runtimeBrands.findIndex(brand => brand.id === brandId);
        if (brandIndex === -1) {
          reject(new Error("Brand not found"));
          return;
        }

        runtimeBrands.splice(brandIndex, 1);
        resolve();
      }, 500);
    });
  }
}
