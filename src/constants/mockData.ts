import { Post } from "@/types";

export const MOCK_POSTS_DATA: Record<string, Post[]> = {
  "nyx-makeup": [
    {
      id: "1",
      brandId: "nyx-makeup",
      title: "New Lipstick Collection Drop",
      content: "Get ready to pucker up! Our new Velvet Matte collection is here with 12 stunning shades 💋✨",
      platform: "Instagram",
      timestamp: "2 hours ago",
      engagement: { likes: 2847, comments: 156, shares: 89 },
      comments: [
        {
          id: "c1",
          author: "beautyqueen_sarah",
          content: "OMG these shades are EVERYTHING! When will they be available at Ulta?",
          timestamp: "1 hour ago",
          aiReply: "We're so excited you love them! 😍 They'll be hitting Ulta shelves this Friday! You can also shop online now with free shipping on orders over $35. Which shade caught your eye? 💄",
          sentiment: "positive"
        },
        {
          id: "c2",
          author: "makeuplover_mike",
          content: "Love the diversity in the campaign but wish there were more nude shades for deeper skin tones",
          timestamp: "45 minutes ago",
          aiReply: "Thank you for this feedback! Inclusivity is at the heart of everything we do. We're always working to expand our shade ranges - keep an eye out for our upcoming launches! 💕 What specific undertones would you love to see?",
          sentiment: "constructive"
        }
      ]
    },
    {
      id: "2",
      brandId: "nyx-makeup",
      title: "Behind the Scenes: Photoshoot Day",
      content: "Take a peek behind the scenes of our latest campaign shoot! The energy was unmatched ✨📸",
      platform: "TikTok",
      timestamp: "4 hours ago",
      engagement: { likes: 15420, comments: 234, shares: 567 },
      comments: [
        {
          id: "c3",
          author: "aspiring_mua",
          content: "The makeup looks are INSANE! Tutorial please? 🙏",
          timestamp: "3 hours ago",
          aiReply: "Yes! We love that you're inspired! 💄 Check out our YouTube channel - we just dropped a step-by-step tutorial for this exact look. Tag us when you recreate it! #NYXTutorial",
          sentiment: "positive"
        }
      ]
    }
  ],
  "belif-skincare": [
    {
      id: "3",
      brandId: "belif-skincare",
      title: "Skincare Routine for Dry Winter Skin",
      content: "Winter got your skin feeling tight and flaky? Our Aqua Bomb collection has your back! ❄️💧",
      platform: "Instagram",
      timestamp: "1 hour ago",
      engagement: { likes: 892, comments: 67, shares: 23 },
      comments: [
        {
          id: "c4",
          author: "skincare_sarah",
          content: "I've been using the Aqua Bomb for 2 weeks and my skin has never been more hydrated! 💙",
          timestamp: "30 minutes ago",
          aiReply: "Sarah, this makes our hearts so happy! 💙 We're thrilled the Aqua Bomb is working its magic for you. Your skin deserves all that hydration! Thanks for sharing your experience with our community ✨",
          sentiment: "positive"
        }
      ]
    }
  ]
};
