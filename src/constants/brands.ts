import { Brand } from "@/types";

export const BRANDS: Brand[] = [
  { id: "nyx-makeup", name: "NYX Professional Makeup", pendingReplies: 12, status: "active" },
  { id: "belif-skincare", name: "belif", pendingReplies: 8, status: "active" },
  { id: "la-roche-posay", name: "<PERSON> Roche-Posay", pendingReplies: 15, status: "active" },
  { id: "maybelline", name: "Maybelline New York", pendingReplies: 3, status: "paused" },
  { id: "thayers", name: "<PERSON>hayers Natural Remedies", pendingReplies: 7, status: "active" },
  { id: "eyebuydirect", name: "EyeBuyDirect", pendingReplies: 5, status: "active" },
  { id: "thinx", name: "THINX", pendingReplies: 2, status: "active" },
  { id: "blueair", name: "Blueair", pendingReplies: 9, status: "active" },
];

export const BRAND_NAMES: Record<string, string> = {
  "nyx-makeup": "NYX Professional Makeup",
  "belif-skincare": "belif",
  "la-roche-posay": "<PERSON>-Posay",
  "maybelline": "Maybelline New York",
  "thayers": "Thayers Natural Remedies",
  "eyebuydirect": "EyeBuyDirect",
  "thinx": "THINX",
  "blueair": "Blueair"
};
