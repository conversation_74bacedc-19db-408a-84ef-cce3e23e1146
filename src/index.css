@tailwind base;
@tailwind components;
@tailwind utilities;

/* ArtisanCouncil Brand Design System */

@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* Base colors - ArtisanCouncil theme with better contrast */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;

    /* Card system - improved contrast */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --card-hover: 0 30% 97%;

    /* Popover system */
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;

    /* Primary brand colors - ArtisanCouncil pink with better contrast */
    --primary: 0 45% 65%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 0 45% 60%;
    --primary-light: 0 45% 96%;

    /* Secondary system - Better contrast grays */
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9%;
    --secondary-hover: 0 0% 94%;

    /* Muted system - improved readability */
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    /* Accent system - ArtisanCouncil accent with better contrast */
    --accent: 0 45% 65%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 0 45% 96%;

    /* Warning system */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 92% 95%;

    /* Destructive system */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border system - subtle but visible */
    --border: 0 0% 89%;
    --input: 0 0% 89%;
    --ring: 0 45% 65%;

    /* AI-specific colors - ArtisanCouncil themed with better contrast */
    --ai-primary: 0 45% 65%;
    --ai-secondary: 0 45% 96%;
    --ai-foreground: 0 0% 100%;

    /* Gradients - ArtisanCouncil themed with better visibility */
    --gradient-primary: linear-gradient(135deg, hsl(0 45% 65%), hsl(0 45% 70%));
    --gradient-ai: linear-gradient(135deg, hsl(0 45% 65%), hsl(0 45% 60%));
    --gradient-subtle: linear-gradient(180deg, hsl(0 0% 100%), hsl(0 0% 98%));

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);
    --shadow-card: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px 0 hsl(0 0% 0% / 0.06);

    /* Spacing and sizing */
    --radius: 0.75rem;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 0 0% 9%;

    --sidebar-primary: 0 45% 65%;

    --sidebar-primary-foreground: 0 0% 100%;

    --sidebar-accent: 0 0% 95%;

    --sidebar-accent-foreground: 0 0% 9%;

    --sidebar-border: 0 0% 91%;

    --sidebar-ring: 0 45% 65%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-weight: 300;
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* High contrast mode */
  .high-contrast {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;
    --primary: 210 100% 70%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 85%;
    --accent: 120 100% 70%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 70%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 30%;
    --input: 0 0% 20%;
    --ring: 210 100% 70%;
  }

  /* Large text mode */
  .large-text {
    font-size: 1.125rem;
  }

  .large-text .text-sm {
    font-size: 1rem;
  }

  .large-text .text-xs {
    font-size: 0.875rem;
  }

  /* Screen reader mode */
  .screen-reader-mode .sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 0.25rem !important;
    margin: 0.25rem !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
    background: hsl(var(--muted));
    border: 1px solid hsl(var(--border));
    border-radius: 0.25rem;
  }

  /* Keyboard navigation */
  .keyboard-navigation input:focus,
  .keyboard-navigation select:focus,
  .keyboard-navigation textarea:focus,
  .keyboard-navigation a:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    box-shadow: 0 0 0 2px hsl(var(--ring));
  }

  /* Explicitly remove focus styles from buttons */
  .keyboard-navigation button:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Focus visible for better keyboard navigation */
  .focus-visible:focus:not(button) {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Ensure buttons never show focus-visible styles */
  button.focus-visible:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Skip link for keyboard users */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    border: 1px solid hsl(var(--border));
    z-index: 1000;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Remove blue border/outline on button clicks and active states */
  button:focus,
  button:active,
  button:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
  }

  /* Ensure buttons still have hover effects but no click borders */
  button:active {
    transform: none !important;
    box-shadow: none !important;
  }

  /* Firefox-specific fixes for Mac */
  @-moz-document url-prefix() {
    input:focus,
    input:active,
    input:focus-visible,
    button:focus,
    button:active,
    button:focus-visible {
      outline: none !important;
      box-shadow: none !important;
      border-color: hsl(var(--border)) !important;
      -moz-appearance: none !important;
    }

    /* Remove Firefox's default focus ring */
    input::-moz-focus-inner,
    button::-moz-focus-inner {
      border: 0 !important;
      padding: 0 !important;
    }
  }

  /* WebKit/Safari specific fixes */
  input:focus,
  input:active {
    outline: none !important;
    -webkit-appearance: none !important;
    box-shadow: none !important;
  }

  /* Additional cross-browser input focus fixes */
  input[type="text"]:focus,
  input[type="search"]:focus,
  textarea:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: hsl(var(--border)) !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}